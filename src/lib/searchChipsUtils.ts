import { SearchChip } from '@/components/SearchChips';
import { SearchCondition } from '@/lib/api';

// Field display name mapping
const FIELD_DISPLAY_NAMES: Record<string, string> = {
  allFields: 'Comprehensive Search',
  productName: 'Product Name',
  companyName: 'Company Name',
  registrationNumber: 'Registration Number',
  specification: 'Specification',
  category: 'Category',
  status: 'Status',
  approvalDate: 'Approval Date',
  expiryDate: 'Expiry Date',
  riskLevel: 'Risk Level',
  deviceClass: 'Device Class',
  manufacturer: 'Manufacturer',
  applicant: 'Applicant',
  // Can continue adding based on actual fields
};

// Operator display name mapping
const OPERATOR_DISPLAY_NAMES: Record<string, string> = {
  contains: 'contains',
  equals: 'equals',
  startsWith: 'starts with',
  endsWith: 'ends with',
  gt: 'greater than',
  gte: 'greater than or equal',
  lt: 'less than',
  lte: 'less than or equal',
  between: 'between',
  in: 'in',
  notIn: 'not in',
  isNull: 'is null',
  isNotNull: 'is not null',
};

/**
 * 将简单过滤器转换为搜索标签
 */
export function filtersToChips(
  filters: Record<string, unknown>,
  fieldConfigs?: Array<{ fieldName: string; displayName: string }>
): SearchChip[] {
  const chips: SearchChip[] = [];

  Object.entries(filters).forEach(([key, value]) => {
    if (value === undefined || value === null || value === '') {
      return;
    }

    // 获取字段显示名称
    const fieldConfig = fieldConfigs?.find(f => f.fieldName === key);
    const fieldDisplayName = fieldConfig?.displayName || FIELD_DISPLAY_NAMES[key] || key;

    // 处理不同类型的值
    let displayValue: string;
    let chipType: SearchChip['type'] = 'filter';

    if (key === 'allFields') {
      chipType = 'query';
      displayValue = `"${value}"`;
    } else if (typeof value === 'boolean') {
      displayValue = `${fieldDisplayName}: ${value ? 'Yes' : 'No'}`;
    } else if (Array.isArray(value)) {
      displayValue = `${fieldDisplayName}: ${value.join(', ')}`;
    } else if (typeof value === 'object' && value !== null) {
      // 处理范围查询
      const rangeValue = value as { from?: string; to?: string };
      if (rangeValue.from && rangeValue.to) {
        displayValue = `${fieldDisplayName}: ${rangeValue.from} - ${rangeValue.to}`;
      } else if (rangeValue.from) {
        displayValue = `${fieldDisplayName}: >= ${rangeValue.from}`;
      } else if (rangeValue.to) {
        displayValue = `${fieldDisplayName}: <= ${rangeValue.to}`;
      } else {
        displayValue = `${fieldDisplayName}: ${JSON.stringify(value)}`;
      }
    } else {
      displayValue = `${fieldDisplayName}: ${value}`;
    }

    chips.push({
      id: `filter_${key}`,
      type: chipType,
      label: fieldDisplayName,
      value,
      displayValue,
      field: key,
      removable: true,
    });
  });

  return chips;
}

/**
 * 将高级搜索条件转换为搜索标签
 */
export function advancedConditionsToChips(
  conditions: SearchCondition[],
  fieldConfigs?: Array<{ fieldName: string; displayName: string }>
): SearchChip[] {
  return conditions.map((condition, index) => {
    // 获取字段显示名称
    const fieldConfig = fieldConfigs?.find(f => f.fieldName === condition.field);
    const fieldDisplayName = fieldConfig?.displayName || FIELD_DISPLAY_NAMES[condition.field] || condition.field;
    
    // 获取操作符显示名称
    const operatorDisplayName = OPERATOR_DISPLAY_NAMES[condition.operator] || condition.operator;

    // 构建显示值
    let displayValue: string;
    if (typeof condition.value === 'object' && condition.value !== null) {
      const rangeValue = condition.value as { from?: string; to?: string };
      if (rangeValue.from && rangeValue.to) {
        displayValue = `${fieldDisplayName} ${operatorDisplayName} ${rangeValue.from} - ${rangeValue.to}`;
      } else if (rangeValue.from) {
        displayValue = `${fieldDisplayName} >= ${rangeValue.from}`;
      } else if (rangeValue.to) {
        displayValue = `${fieldDisplayName} <= ${rangeValue.to}`;
      } else {
        displayValue = `${fieldDisplayName} ${operatorDisplayName} ${JSON.stringify(condition.value)}`;
      }
    } else {
      displayValue = `${fieldDisplayName} ${operatorDisplayName} ${condition.value}`;
    }

    // 添加逻辑操作符前缀（除了第一个条件）
    if (index > 0 && condition.logic) {
      displayValue = `${condition.logic} ${displayValue}`;
    }

    return {
      id: `advanced_${condition.id}`,
      type: 'advanced' as const,
      label: fieldDisplayName,
      value: condition.value,
      displayValue,
      field: condition.field,
      operator: condition.operator,
      removable: true,
    };
  });
}

/**
 * 将排序条件转换为搜索标签
 */
export function sortToChip(
  sortBy?: string,
  sortOrder?: 'asc' | 'desc',
  fieldConfigs?: Array<{ fieldName: string; displayName: string }>
): SearchChip | null {
  if (!sortBy) return null;

  const fieldConfig = fieldConfigs?.find(f => f.fieldName === sortBy);
  const fieldDisplayName = fieldConfig?.displayName || FIELD_DISPLAY_NAMES[sortBy] || sortBy;
  const orderDisplayName = sortOrder === 'asc' ? 'Ascending' : 'Descending';

  return {
    id: 'sort',
    type: 'sort',
    label: 'Sort',
    value: `${sortBy}_${sortOrder}`,
    displayValue: `Sort: ${fieldDisplayName} (${orderDisplayName})`,
    field: sortBy,
    operator: sortOrder,
    removable: true,
  };
}

/**
 * 组合所有搜索条件为标签数组
 */
export function combineSearchChips(
  filters: Record<string, unknown>,
  advancedConditions: SearchCondition[],
  sortBy?: string,
  sortOrder?: 'asc' | 'desc',
  fieldConfigs?: Array<{ fieldName: string; displayName: string }>
): SearchChip[] {
  const chips: SearchChip[] = [];

  // 添加过滤器标签
  chips.push(...filtersToChips(filters, fieldConfigs));

  // 添加高级搜索标签
  chips.push(...advancedConditionsToChips(advancedConditions, fieldConfigs));

  // 添加排序标签
  const sortChip = sortToChip(sortBy, sortOrder, fieldConfigs);
  if (sortChip) {
    chips.push(sortChip);
  }

  return chips;
}

/**
 * 从标签ID解析出移除操作的类型和参数
 */
export function parseChipRemoval(chipId: string): {
  type: 'filter' | 'advanced' | 'sort';
  key?: string;
  conditionId?: string;
} {
  if (chipId.startsWith('filter_')) {
    return {
      type: 'filter',
      key: chipId.replace('filter_', ''),
    };
  } else if (chipId.startsWith('advanced_')) {
    return {
      type: 'advanced',
      conditionId: chipId.replace('advanced_', ''),
    };
  } else if (chipId === 'sort') {
    return {
      type: 'sort',
    };
  }

  throw new Error(`Unknown chip ID format: ${chipId}`);
}
